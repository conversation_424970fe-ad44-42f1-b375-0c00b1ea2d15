import * as React from "react";

function IconTicket({
  size = 24,
  color = "currentColor",
  stroke = 2,
  ...props
}) {
  return <svg xmlns="http://www.w3.org/2000/svg" className="icon icon-tabler icon-tabler-ticket" width={size} height={size} viewBox="0 0 24 24" strokeWidth={stroke} stroke={color} fill="none" strokeLinecap="round" strokeLinejoin="round" {...props}><desc>{"Download more icon variants from https://tabler-icons.io/i/ticket"}</desc><path stroke="none" d="M0 0h24v24H0z" fill="none" /><line x1={15} y1={5} x2={15} y2={7} /><line x1={15} y1={11} x2={15} y2={13} /><line x1={15} y1={17} x2={15} y2={19} /><path d="M5 5h14a2 2 0 0 1 2 2v3a2 2 0 0 0 0 4v3a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2v-3a2 2 0 0 0 0 -4v-3a2 2 0 0 1 2 -2" /></svg>;
}

export default IconTicket;