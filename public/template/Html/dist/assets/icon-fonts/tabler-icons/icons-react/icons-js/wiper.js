import * as React from "react";

function IconWiper({
  size = 24,
  color = "currentColor",
  stroke = 2,
  ...props
}) {
  return <svg xmlns="http://www.w3.org/2000/svg" className="icon icon-tabler icon-tabler-wiper" width={size} height={size} viewBox="0 0 24 24" strokeWidth={stroke} stroke={color} fill="none" strokeLinecap="round" strokeLinejoin="round" {...props}><desc>{"Download more icon variants from https://tabler-icons.io/i/wiper"}</desc><path stroke="none" d="M0 0h24v24H0z" fill="none" /><circle cx={12} cy={18} r={1} /><path d="M3 9l5.5 5.5a5 5 0 0 1 7 0l5.5 -5.5a12 12 0 0 0 -18 0" /><line x1={12} y1={18} x2={9.8} y2={5.2} /></svg>;
}

export default IconWiper;