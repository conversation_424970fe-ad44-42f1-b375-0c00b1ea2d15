import * as React from "react";

function IconSubmarine({
  size = 24,
  color = "currentColor",
  stroke = 2,
  ...props
}) {
  return <svg xmlns="http://www.w3.org/2000/svg" className="icon icon-tabler icon-tabler-submarine" width={size} height={size} viewBox="0 0 24 24" strokeWidth={stroke} stroke={color} fill="none" strokeLinecap="round" strokeLinejoin="round" {...props}><desc>{"Download more icon variants from https://tabler-icons.io/i/submarine"}</desc><path stroke="none" d="M0 0h24v24H0z" fill="none" /><path d="M3 11v6h2l1 -1.5l3 1.5h10a3 3 0 0 0 0 -6h-10h0l-3 1.5l-1 -1.5h-2z" /><path d="M17 11l-1 -3h-5l-1 3" /><path d="M13 8v-2a1 1 0 0 1 1 -1h1" /></svg>;
}

export default IconSubmarine;