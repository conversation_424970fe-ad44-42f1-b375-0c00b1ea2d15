import * as React from "react";

const IconTrackPrev = (size = 24, color = "currentColor", stroke = 2, ...props) => <svg className="icon icon-tabler icon-tabler-track-prev" width={size} height={size} viewBox="0 0 24 24" strokeWidth={stroke} stroke={color} fill="none" strokeLinecap="round" strokeLinejoin="round" {...props}><path stroke="none" d="M0 0h24v24H0z" fill="none" /><path d="M10.31 19.802l-6.56 -6.249c-1 -.799 -1 -2.307 0 -3.106l6.564 -6.252c.67 -.48 1.686 0 1.686 .805v4l5.394 -4.808c.669 -.478 1.606 0 1.606 .808v14c0 .812 -.936 1.285 -1.602 .809l-5.398 -4.809v4c0 .816 -1.02 1.281 -1.69 .802z" /></svg>;

export default IconTrackPrev;