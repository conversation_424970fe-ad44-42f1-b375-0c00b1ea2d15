import * as React from "react";

const IconSkipForward = (size = 24, color = "currentColor", stroke = 2, ...props) => <svg className="icon icon-tabler icon-tabler-skip-forward" width={size} height={size} viewBox="0 0 24 24" strokeWidth={stroke} stroke={color} fill="none" strokeLinecap="round" strokeLinejoin="round" {...props}><path stroke="none" d="M0 0h24v24H0z" fill="none" /><path d="M4 6v12a2 2 0 0 0 2.75 1.84l8.25 -6.1a2 2 0 0 0 0 -3.5l-8.25 -6.1a2 2 0 0 0 -2.75 1.75" /><line x1={20} y1={4} x2={20} y2={20} /></svg>;

export default IconSkipForward;