import * as React from "react";

function IconToggleLeft({
  size = 24,
  color = "currentColor",
  stroke = 2,
  ...props
}) {
  return <svg xmlns="http://www.w3.org/2000/svg" className="icon icon-tabler icon-tabler-toggle-left" width={size} height={size} viewBox="0 0 24 24" strokeWidth={stroke} stroke={color} fill="none" strokeLinecap="round" strokeLinejoin="round" {...props}><desc>{"Download more icon variants from https://tabler-icons.io/i/toggle-left"}</desc><path stroke="none" d="M0 0h24v24H0z" fill="none" /><circle cx={8} cy={12} r={2} /><rect x={2} y={6} width={20} height={12} rx={6} /></svg>;
}

export default IconToggleLeft;