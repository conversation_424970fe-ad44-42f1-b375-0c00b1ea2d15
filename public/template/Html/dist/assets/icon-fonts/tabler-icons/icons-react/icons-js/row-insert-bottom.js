import * as React from "react";

function IconRowInsertBottom({
  size = 24,
  color = "currentColor",
  stroke = 2,
  ...props
}) {
  return <svg xmlns="http://www.w3.org/2000/svg" className="icon icon-tabler icon-tabler-row-insert-bottom" width={size} height={size} viewBox="0 0 24 24" strokeWidth={stroke} stroke={color} fill="none" strokeLinecap="round" strokeLinejoin="round" {...props}><desc>{"Download more icon variants from https://tabler-icons.io/i/row-insert-bottom"}</desc><path stroke="none" d="M0 0h24v24H0z" fill="none" /><path d="M20 6v4a1 1 0 0 1 -1 1h-14a1 1 0 0 1 -1 -1v-4a1 1 0 0 1 1 -1h14a1 1 0 0 1 1 1z" /><line x1={12} y1={15} x2={12} y2={19} /><line x1={14} y1={17} x2={10} y2={17} /></svg>;
}

export default IconRowInsertBottom;