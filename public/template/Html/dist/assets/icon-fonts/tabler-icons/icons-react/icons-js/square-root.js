import * as React from "react";

function IconSquareRoot({
  size = 24,
  color = "currentColor",
  stroke = 2,
  ...props
}) {
  return <svg xmlns="http://www.w3.org/2000/svg" className="icon icon-tabler icon-tabler-square-root" width={size} height={size} viewBox="0 0 24 24" strokeWidth={stroke} stroke={color} fill="none" strokeLinecap="round" strokeLinejoin="round" {...props}><desc>{"Download more icon variants from https://tabler-icons.io/i/square-root"}</desc><path stroke="none" d="M0 0h24v24H0z" fill="none" /><path d="M3 12h2l4 8l4 -16h8" /></svg>;
}

export default IconSquareRoot;