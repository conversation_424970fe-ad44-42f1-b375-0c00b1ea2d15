import * as React from "react";

function IconPlayerSkipForward({
  size = 24,
  color = "currentColor",
  stroke = 2,
  ...props
}) {
  return <svg xmlns="http://www.w3.org/2000/svg" className="icon icon-tabler icon-tabler-player-skip-forward" width={size} height={size} viewBox="0 0 24 24" strokeWidth={stroke} stroke={color} fill="none" strokeLinecap="round" strokeLinejoin="round" {...props}><desc>{"Download more icon variants from https://tabler-icons.io/i/player-skip-forward"}</desc><path stroke="none" d="M0 0h24v24H0z" fill="none" /><path d="M4 5v14l12 -7z" /><line x1={20} y1={5} x2={20} y2={19} /></svg>;
}

export default IconPlayerSkipForward;